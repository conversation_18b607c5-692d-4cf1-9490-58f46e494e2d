import AnalyticsChart from "@/components/dashboard/analytics-chart";
import MetricsCard from "@/components/dashboard/metrics-card";
import RealTimeVisitors from "@/components/dashboard/real-time-visitors";
import TopSources from "@/components/dashboard/top-sources";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import {
  getDefaultDateRange,
  getTimeSeriesData,
  getTrafficSources,
  getWebsiteOverview,
} from "@/lib/tinybird/analytics";
import { eq } from "drizzle-orm";
import {
  BarChart3,
  DollarSign,
  Globe,
  MousePointer,
  TrendingUp,
  Users,
  Activity,
  Sparkles,
  ArrowUpRight,
  Calendar,
  Clock,
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function DashboardPage() {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  // Get user's first website for demo data
  // In a full implementation, this would aggregate data across all websites
  const userWebsites = await db
    .select()
    .from(websites)
    .where(eq(websites.userId, session.user.id))
    .limit(1);

  if (!userWebsites.length) {
    // Enhanced empty state with modern design
    return (
      <div className="min-h-[80vh] flex flex-col">
        {/* Enhanced Header */}
        <div className="mb-8">
          <div className="flex items-start space-x-3 mb-3">
            <div className="p-2 mt-1  rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                Dashboard
              </h1>
              <p className="text-slate-600 text-sm font-medium">
                Welcome to InstaSight Analytics
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Empty State Card */}
        <div className="flex-1 flex items-center justify-center">
          <Card className="max-w-lg w-full mx-auto border-0 shadow-xl bg-gradient-to-br from-white to-slate-50/50 backdrop-blur-sm">
            <CardContent className="pt-12 pb-8 px-8">
              <div className="text-center space-y-6">
                {/* Animated Icon */}
                <div className="relative">
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 opacity-20 animate-pulse"></div>
                  <div className="relative p-6 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                    <Globe className="h-12 w-12 text-white" />
                  </div>
                </div>

                {/* Enhanced Content */}
                <div className="space-y-3">
                  <h3 className="text-2xl font-bold text-slate-900">
                    Get Started with Analytics
                  </h3>
                  <p className="text-slate-600 leading-relaxed">
                    Add your first website to unlock powerful insights about
                    your visitors, track conversions, and grow your business
                    with data-driven decisions.
                  </p>
                </div>

                {/* Enhanced CTA */}
                <div className="pt-4">
                  <Button
                    asChild
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 px-8 py-3 text-base font-semibold"
                  >
                    <Link
                      href="/dashboard/websites/new"
                      className="flex items-center space-x-2"
                    >
                      <Globe className="h-5 w-5" />
                      <span>Add Your First Website</span>
                      <ArrowUpRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const website = userWebsites[0];
  const dateRange = getDefaultDateRange();

  // Get analytics data from Tinybird
  let overviewData: any;
  let chartData: any;
  let topSources: any;

  try {
    const [overview, timeSeriesData, trafficSources] = await Promise.all([
      getWebsiteOverview({
        website_id: website.trackingId,
        start_date: dateRange.from.toISOString().split("T")[0],
        end_date: dateRange.to.toISOString().split("T")[0],
      }),
      getTimeSeriesData(
        {
          website_id: website.trackingId,
          start_date: dateRange.from.toISOString().split("T")[0],
          end_date: dateRange.to.toISOString().split("T")[0],
        },
        "visitors",
        "day"
      ),
      getTrafficSources(
        {
          website_id: website.trackingId,
          start_date: dateRange.from.toISOString().split("T")[0],
          end_date: dateRange.to.toISOString().split("T")[0],
        },
        5
      ),
    ]);

    overviewData = overview;
    chartData = timeSeriesData.map((point) => ({
      date: new Date(point.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
      visitors: point.value,
      pageviews: point.value * 1.8, // Approximate ratio
      revenue: point.value * 0.5, // Approximate ratio
    }));
    topSources = trafficSources.map((source) => ({
      name: source.source || "Direct",
      visitors: source.visitors,
      percentage:
        (source.visitors / Math.max(overview?.visitors || 1, 1)) * 100,
      revenue: source.revenue,
      change: Math.round((Math.random() - 0.5) * 20), // Mock change for now
    }));
  } catch (error) {
    console.error("Failed to fetch analytics data:", error);
    // Fall back to mock data
    overviewData = {
      visitors: 12345,
      pageviews: 24000,
      bounce_rate: 65.2,
      avg_session_duration: 180,
      total_revenue: 45231,
    };
    chartData = [
      { date: "Jan 1", visitors: 1200, pageviews: 2400, revenue: 1200 },
      { date: "Jan 2", visitors: 1800, pageviews: 3200, revenue: 1800 },
      { date: "Jan 3", visitors: 1600, pageviews: 2800, revenue: 1600 },
      { date: "Jan 4", visitors: 2200, pageviews: 4100, revenue: 2400 },
      { date: "Jan 5", visitors: 1900, pageviews: 3600, revenue: 2100 },
      { date: "Jan 6", visitors: 2400, pageviews: 4800, revenue: 2800 },
      { date: "Jan 7", visitors: 2100, pageviews: 4200, revenue: 2600 },
    ];
    topSources = [
      {
        name: "Google",
        visitors: 8420,
        percentage: 42.1,
        revenue: 15600,
        change: 12.5,
      },
      {
        name: "Direct",
        visitors: 5230,
        percentage: 26.2,
        revenue: 9800,
        change: 8.3,
      },
      {
        name: "Facebook",
        visitors: 3100,
        percentage: 15.5,
        revenue: 4200,
        change: -2.1,
      },
      {
        name: "Twitter",
        visitors: 1850,
        percentage: 9.3,
        revenue: 2100,
        change: 5.7,
      },
      {
        name: "LinkedIn",
        visitors: 1400,
        percentage: 7.0,
        revenue: 3400,
        change: 15.2,
      },
    ];
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      <div className="container mx-auto max-w-7xl px-4 py-6 space-y-8">
        {/* Enhanced Header with Breadcrumb and Actions */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm text-slate-500">
              <span>InstaSight</span>
              <span>/</span>
              <span className="text-slate-900 font-medium">Dashboard</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                  Analytics Dashboard
                </h1>
                <p className="text-slate-600 font-medium">
                  Real-time insights and performance metrics for{" "}
                  {website.name || website.domain}
                </p>
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 px-3 py-2 bg-white rounded-lg border border-slate-200 shadow-sm">
              <Calendar className="h-4 w-4 text-slate-500" />
              <span className="text-sm font-medium text-slate-700">
                Last 7 days
              </span>
            </div>
            <Button
              variant="outline"
              className="border-slate-200 hover:bg-slate-50 transition-colors"
            >
              <Activity className="h-4 w-4 mr-2" />
              Live View
            </Button>
          </div>
        </div>

        {/* Enhanced Metrics Cards with Animation */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-slate-900 flex items-center space-x-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              <span>Key Metrics</span>
            </h2>
            <div className="flex items-center space-x-2 text-sm text-slate-500">
              <Clock className="h-4 w-4" />
              <span>Updated 2 minutes ago</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="transform hover:scale-105 transition-all duration-300">
              <MetricsCard
                title="Total Visitors"
                value={overviewData?.visitors || 0}
                change={{ value: 10.2, label: "from last month" }}
                icon={Users}
                className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-blue-50/30 backdrop-blur-sm"
              />
            </div>

            <div className="transform hover:scale-105 transition-all duration-300 delay-75">
              <MetricsCard
                title="Page Views"
                value={overviewData?.pageviews || 0}
                change={{ value: 15.3, label: "from last month" }}
                icon={BarChart3}
                className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-orange-50/30 backdrop-blur-sm"
              />
            </div>

            <div className="transform hover:scale-105 transition-all duration-300 delay-150">
              <MetricsCard
                title="Revenue"
                value={overviewData?.total_revenue || 0}
                change={{ value: 25.1, label: "from last month" }}
                icon={DollarSign}
                prefix="$"
                className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-green-50/30 backdrop-blur-sm"
              />
            </div>

            <div className="transform hover:scale-105 transition-all duration-300 delay-200">
              <MetricsCard
                title="Bounce Rate"
                value={overviewData?.bounce_rate?.toFixed(1) || "0.0"}
                change={{ value: -2.3, label: "from last month" }}
                icon={MousePointer}
                suffix="%"
                className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-purple-50/30 backdrop-blur-sm"
              />
            </div>
          </div>
        </div>

        {/* Enhanced Charts Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-slate-900 flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-green-500" />
            <span>Performance Analytics</span>
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="transform hover:scale-[1.02] transition-all duration-300">
              <Card className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-blue-50/20 backdrop-blur-sm overflow-hidden">
                <AnalyticsChart
                  data={chartData}
                  type="area"
                  metric="visitors"
                  title="Visitors Over Time"
                  description="Daily unique visitors to your website"
                />
              </Card>
            </div>

            <div className="transform hover:scale-[1.02] transition-all duration-300 delay-75">
              <Card className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-green-50/20 backdrop-blur-sm overflow-hidden">
                <AnalyticsChart
                  data={chartData}
                  type="line"
                  metric="revenue"
                  title="Revenue Trend"
                  description="Daily revenue from your website"
                />
              </Card>
            </div>
          </div>
        </div>

        {/* Enhanced Main Content Area */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-slate-900 flex items-center space-x-2">
            <Activity className="h-5 w-5 text-purple-500" />
            <span>Live Analytics</span>
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Enhanced Real-time Visitors */}
            <div className="transform hover:scale-[1.02] transition-all duration-300">
              <div className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-purple-50/20 backdrop-blur-sm rounded-xl overflow-hidden">
                <RealTimeVisitors websiteId={website.trackingId} />
              </div>
            </div>

            {/* Enhanced Top Traffic Sources */}
            <div className="transform hover:scale-[1.02] transition-all duration-300 delay-75">
              <div className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-indigo-50/20 backdrop-blur-sm rounded-xl overflow-hidden">
                <TopSources sources={topSources} />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Recent Activity & Top Pages */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-slate-900 flex items-center space-x-2">
            <Clock className="h-5 w-5 text-orange-500" />
            <span>Recent Insights</span>
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Enhanced Recent Activity */}
            <Card className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-orange-50/20 backdrop-blur-sm transition-all duration-300 hover:scale-[1.02]">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 shadow-sm">
                    <Activity className="h-4 w-4 text-white" />
                  </div>
                  <span>Recent Activity</span>
                </CardTitle>
                <CardDescription>
                  Latest events from your websites
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-green-50/50 rounded-xl border border-green-100 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-green-500 shadow-sm">
                        <DollarSign className="h-3 w-3 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900">
                          New purchase
                        </p>
                        <p className="text-sm text-slate-600">
                          example.com • $99.00
                        </p>
                      </div>
                    </div>
                    <span className="text-xs text-slate-500 font-medium">
                      2 min ago
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-50/50 rounded-xl border border-blue-100 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-blue-500 shadow-sm">
                        <Users className="h-3 w-3 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900">
                          New visitor
                        </p>
                        <p className="text-sm text-slate-600">
                          example.com • /pricing
                        </p>
                      </div>
                    </div>
                    <span className="text-xs text-slate-500 font-medium">
                      5 min ago
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-purple-50/50 rounded-xl border border-purple-100 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-purple-500 shadow-sm">
                        <TrendingUp className="h-3 w-3 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900">
                          Goal completed
                        </p>
                        <p className="text-sm text-slate-600">
                          example.com • Newsletter signup
                        </p>
                      </div>
                    </div>
                    <span className="text-xs text-slate-500 font-medium">
                      12 min ago
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Top Pages */}
            <Card className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-indigo-50/20 backdrop-blur-sm transition-all duration-300 hover:scale-[1.02]">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-500 to-indigo-600 shadow-sm">
                    <Globe className="h-4 w-4 text-white" />
                  </div>
                  <span>Top Pages</span>
                </CardTitle>
                <CardDescription>Most visited pages this week</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-slate-50/50 rounded-xl border border-slate-100 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-slate-600 shadow-sm">
                        <Globe className="h-3 w-3 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900">/</p>
                        <p className="text-sm text-slate-600">Homepage</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-slate-900">2,341</p>
                      <p className="text-xs text-green-600 font-medium">+12%</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-slate-50/50 rounded-xl border border-slate-100 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-slate-600 shadow-sm">
                        <DollarSign className="h-3 w-3 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900">/pricing</p>
                        <p className="text-sm text-slate-600">Pricing page</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-slate-900">1,832</p>
                      <p className="text-xs text-green-600 font-medium">+8%</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-slate-50/50 rounded-xl border border-slate-100 hover:shadow-sm transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-full bg-slate-600 shadow-sm">
                        <BarChart3 className="h-3 w-3 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900">
                          /features
                        </p>
                        <p className="text-sm text-slate-600">Features page</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-slate-900">1,243</p>
                      <p className="text-xs text-green-600 font-medium">+5%</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Enhanced Quick Actions */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-slate-900 flex items-center space-x-2">
            <Sparkles className="h-5 w-5 text-pink-500" />
            <span>Quick Actions</span>
          </h2>

          <Card className="border-0 shadow-lg hover:shadow-xl bg-gradient-to-br from-white to-pink-50/20 backdrop-blur-sm transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-slate-900">
                Get Started with InstaSight
              </CardTitle>
              <CardDescription>
                Powerful analytics tools to grow your business
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  className="h-24 flex-col border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 hover:from-blue-100 hover:to-blue-200/50 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                  asChild
                >
                  <Link href="/dashboard/websites/new" className="space-y-2">
                    <div className="p-2 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 shadow-sm">
                      <Globe className="h-5 w-5 text-white" />
                    </div>
                    <span className="font-semibold text-slate-900">
                      Add Website
                    </span>
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  className="h-24 flex-col border-0 bg-gradient-to-br from-green-50 to-green-100/50 hover:from-green-100 hover:to-green-200/50 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 delay-75"
                  asChild
                >
                  <Link href="/dashboard/goals" className="space-y-2">
                    <div className="p-2 rounded-full bg-gradient-to-br from-green-500 to-green-600 shadow-sm">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <span className="font-semibold text-slate-900">
                      Set Goals
                    </span>
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  className="h-24 flex-col border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 hover:from-purple-100 hover:to-purple-200/50 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 delay-150"
                  asChild
                >
                  <Link href="/dashboard/settings" className="space-y-2">
                    <div className="p-2 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 shadow-sm">
                      <BarChart3 className="h-5 w-5 text-white" />
                    </div>
                    <span className="font-semibold text-slate-900">
                      View Reports
                    </span>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
